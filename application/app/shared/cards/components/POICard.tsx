/** @format */

'use client';

// Extend window interface for debug logging
declare global {
	interface Window {
		__loggedPOIs?: Set<string>;
	}
}

import { colors } from '@/app/colors';
import UnifiedInteractionButtons from '@/app/shared/userInteractions/components/UnifiedInteractionButtons';
import React from 'react';
import {
	FaClock,
	FaExternalLinkAlt,
	FaMapMarkerAlt,
	FaPhone,
	FaStar,
	FaTimes,
} from 'react-icons/fa';
import BaseCard from './BaseCard';
import POIImageCarousel from './POIImageCarousel';
import { BasePOI, POICardProps } from './types';
import {
	formatDistance,
	formatRating,
	generateSearchUrl,
	getCategoryIcon,
	getLocationString,
} from './utils';

// Import MediaItem from shared types
import { MediaItem } from '@/app/shared/media/types';

// Extended interface for unified interaction system
interface POIInteractionData {
	poi_id: number;
	poi_type: string;
	like_count: number;
	favorite_count: number;
	visit_count: number;
	review_count: number;
	media_count: number;
	user_has_liked: boolean;
	user_has_favorited: boolean;
	user_has_visited: boolean;
}

interface ExtendedPOICardProps extends POICardProps {
	// Optional batch loading props
	onLoadInteractions?: (
		pois: Array<{ poi_id: number; poi_type: string }>
	) => void;
	interactionData?: POIInteractionData | null;
	onToggleInteraction?: (
		poiId: number,
		poiType: string,
		interactionType: 'like' | 'favorite' | 'visit',
		action: 'add' | 'remove'
	) => void;
	// Legacy callback for backward compatibility
	onFavoriteToggle?: (poi: BasePOI) => void;
	// Flag to indicate if parent is managing batch loading
	useBatchLoading?: boolean;
	// Pre-loaded media data to avoid individual API calls
	mediaData?: MediaItem[];
	// Function to refresh interaction data after user actions
	onRefreshInteractions?: () => Promise<void>;
	// UI options
	showPoiId?: boolean; // Whether to show POI ID badge
	useSimpleUI?: boolean; // Whether to use simplified UI (OptimizedPOICard style)
	// Option to disable auto-loading of interactions (for performance in lists)
	disableInteractionAutoLoad?: boolean;
}

const POICard: React.FC<ExtendedPOICardProps> = ({
	poi,
	isVisible,
	onClose,
	onNavigate,
	showActions = true,
	variant = 'modal',
	// Batch loading props
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	onLoadInteractions,
	interactionData,
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	onToggleInteraction,
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	onFavoriteToggle,
	useBatchLoading = false,
	mediaData,
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	onRefreshInteractions,
	// UI options
	showPoiId = true,
	useSimpleUI = false,
	// New option to disable auto-loading of interactions
	disableInteractionAutoLoad = false,
}) => {
	// Safety check for null POI
	if (!poi) {
		return null;
	}

	// poiForLazyLoad variable removed as it was unused

	// Action handlers
	const handleOpenMaps = () => {
		const url = generateSearchUrl(poi);
		window.open(url, '_blank');
	};

	const handleViewDetails = () => {
		if (onNavigate) {
			onNavigate(poi);
		}
	};

	// Card configuration
	const cardConfig = {
		maxWidth: variant === 'inline' ? '100%' : '400px',
		animation: (variant === 'modal' ? 'slide' : 'fade') as
			| 'slide'
			| 'fade'
			| 'scale',
	};

	const cardContent = (
		<div className='flex flex-col h-full group'>
			{/* Modern Image Section with Overlay */}
			<div className='relative overflow-hidden'>
				<POIImageCarousel
					poiId={poi.poi_id || poi.approved_id || poi.temp_id || ''}
					poiType={poi.poi_type || 'official'}
					height='h-52'
					showDots={true}
					autoPlay={false}
					preloadedMedia={mediaData}
					disableApiLoading={useBatchLoading && !!mediaData}
				/>

				{/* Gradient Overlay for better text readability */}
				<div className='absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>

				{/* Modern POI ID Badge - Conditional */}
				{showPoiId && !useSimpleUI && (
					<div className='absolute top-3 left-3 z-10'>
						<div className='inline-flex items-center gap-1.5 px-3 py-1.5 rounded-xl bg-white/90 backdrop-blur-sm border border-white/20 shadow-lg'>
							<div className='w-2 h-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full'></div>
							<span className='text-xs font-semibold text-gray-700'>
								#{poi.poi_id || poi.approved_id || poi.temp_id || 'N/A'}
							</span>
						</div>
					</div>
				)}

				{/* Simple Category Badge - For Simple UI */}
				{useSimpleUI && (
					<div className='absolute top-3 left-3 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700 shadow-sm border border-white/20'>
						<span className='mr-1'>{getCategoryIcon(poi.category)}</span>
						{poi.category || 'POI'}
					</div>
				)}

				{/* Category Icon Badge */}
				<div className='absolute top-3 right-3 z-10'>
					<div className='w-10 h-10 bg-white/90 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg flex items-center justify-center'>
						<span className='text-lg'>{getCategoryIcon(poi.category)}</span>
					</div>
				</div>

				{/* Close button for modal variant */}
				{variant !== 'inline' && (
					<button
						onClick={onClose}
						className='absolute top-3 right-3 z-20 p-2 rounded-xl bg-white/90 backdrop-blur-sm border border-white/20 text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-200 shadow-lg'>
						<FaTimes className='w-4 h-4' />
					</button>
				)}

				{/* Hover Action Overlay */}
				<div className='absolute inset-x-3 bottom-3 z-10 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300'>
					<button
						onClick={handleViewDetails}
						className='w-full py-2.5 px-4 bg-white/95 backdrop-blur-sm rounded-xl border border-white/20 text-gray-800 font-medium text-sm hover:bg-white transition-all duration-200 shadow-lg flex items-center justify-center gap-2'>
						<FaExternalLinkAlt className='w-3.5 h-3.5' />
						Quick View
					</button>
				</div>
			</div>

			{/* Modern Header Section */}
			<div className='px-5 py-4 border-b border-gray-50'>
				<div className='space-y-2'>
					<h3 className='text-lg font-bold text-gray-900 leading-tight line-clamp-2 group-hover:text-blue-600 transition-colors duration-200'>
						{poi.name}
					</h3>
					<div className='flex items-center gap-2'>
						<div className='flex items-center gap-1.5 px-2.5 py-1 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg border border-blue-100'>
							<div className='w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full'></div>
							<span className='text-xs font-medium text-blue-700 capitalize'>
								{poi?.category ? poi.category.replace('_', ' ') : 'Location'}
							</span>
						</div>
						{poi?.subcategory && (
							<span className='text-xs text-gray-500 font-medium capitalize'>
								{poi.subcategory.replace('_', ' ')}
							</span>
						)}
					</div>
				</div>
			</div>

			{/* Modern Content Section */}
			<div className='flex-1 px-5 py-4 space-y-4'>
				{/* Location with modern styling */}
				<div className='flex items-start gap-3'>
					<div className='w-8 h-8 bg-gradient-to-br from-red-100 to-pink-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5'>
						<FaMapMarkerAlt className='w-3.5 h-3.5 text-red-500' />
					</div>
					<div className='flex-1 min-w-0 space-y-1'>
						<p className='text-sm font-medium text-gray-800 break-words leading-relaxed'>
							{getLocationString(poi)}
						</p>
						{poi.distance_km && (
							<div className='inline-flex items-center gap-1 px-2 py-0.5 bg-blue-50 rounded-md'>
								<div className='w-1.5 h-1.5 bg-blue-500 rounded-full'></div>
								<span className='text-xs font-medium text-blue-700'>
									{formatDistance(poi.distance_km)}
								</span>
							</div>
						)}
					</div>
				</div>

				{/* Rating with modern design */}
				{poi.user_rating_avg && (
					<div className='flex items-center gap-3'>
						<div className='w-8 h-8 bg-gradient-to-br from-yellow-100 to-orange-100 rounded-lg flex items-center justify-center flex-shrink-0'>
							<FaStar className='w-3.5 h-3.5 text-yellow-500' />
						</div>
						<div className='flex-1'>
							<p className='text-sm font-medium text-gray-800'>
								{formatRating(poi.user_rating_avg, poi.user_rating_count)}
							</p>
						</div>
					</div>
				)}

				{/* Phone with modern styling */}
				{poi.phone_number && (
					<div className='flex items-center gap-3'>
						<div className='w-8 h-8 bg-gradient-to-br from-green-100 to-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0'>
							<FaPhone className='w-3.5 h-3.5 text-green-500' />
						</div>
						<div className='flex-1'>
							<a
								href={`tel:${poi.phone_number}`}
								className='text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200'>
								{poi.phone_number}
							</a>
						</div>
					</div>
				)}

				{/* Hours with modern design */}
				{poi.opening_hours && (
					<div className='flex items-start gap-3'>
						<div className='w-8 h-8 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5'>
							<FaClock className='w-3.5 h-3.5 text-purple-500' />
						</div>
						<div className='flex-1'>
							<p className='text-sm text-gray-700 whitespace-pre-line leading-relaxed font-medium'>
								{poi.opening_hours}
							</p>
						</div>
					</div>
				)}

				{/* Description with better typography */}
				{poi.description && (
					<div className='pt-2 border-t border-gray-50'>
						<p className='text-sm text-gray-600 line-clamp-3 leading-relaxed'>
							{poi.description}
						</p>
					</div>
				)}
			</div>

			{/* Modern Footer with Actions */}
			{showActions && (
				<div className='border-t border-gray-50 bg-gradient-to-r from-gray-50/50 to-white/50 backdrop-blur-sm px-5 py-4 rounded-b-2xl'>
					{/* User Interaction Buttons - Simple Style */}
					<div className='flex justify-center mb-4'>
						<div>
							{(() => {
								const poiIdForOfficial =
									poi.poi_type === 'official'
										? poi.poi_id || poi.id
										: undefined;
								const userPoiTempIdForTemp =
									poi.poi_type === 'user_temp'
										? poi.temp_id || poi.id
										: undefined;
								const userPoiApprovedIdForApproved =
									poi.poi_type === 'user_approved'
										? poi.approved_id || poi.id
										: undefined;

								// Only log once per POI to avoid render loops
								if (typeof window !== 'undefined' && !window.__loggedPOIs) {
									window.__loggedPOIs = new Set();
								}
								const poiKey = `${poi.poi_type}-${
									poiIdForOfficial ||
									userPoiTempIdForTemp ||
									userPoiApprovedIdForApproved
								}`;
								if (
									typeof window !== 'undefined' &&
									window.__loggedPOIs &&
									!window.__loggedPOIs.has(poiKey)
								) {
									console.log('POICard UnifiedInteractionButtons props:', {
										poi: poi,
										poiId: poiIdForOfficial,
										userPoiTempId: userPoiTempIdForTemp,
										userPoiApprovedId: userPoiApprovedIdForApproved,
										poiType: poi.poi_type,
										autoLoad: !disableInteractionAutoLoad,
									});
									window.__loggedPOIs.add(poiKey);
								}

								return (
									<UnifiedInteractionButtons
										poiId={poiIdForOfficial}
										userPoiTempId={userPoiTempIdForTemp}
										userPoiApprovedId={userPoiApprovedIdForApproved}
										poiType={
											(poi.poi_type as
												| 'official'
												| 'user_temp'
												| 'user_approved') || 'official'
										}
										initialData={
											interactionData
												? {
														like_count: interactionData.like_count,
														favorite_count: interactionData.favorite_count,
														visit_count: interactionData.visit_count,
														review_count: interactionData.review_count || 0,
														user_has_liked: interactionData.user_has_liked,
														user_has_favorited:
															interactionData.user_has_favorited,
														user_has_visited: interactionData.user_has_visited,
												  }
												: undefined
										}
										layout='horizontal'
										showCounts={true}
										showLabels={false}
										size='sm'
										enableOptimisticUpdates={true}
										autoLoad={!disableInteractionAutoLoad}
									/>
								);
							})()}
						</div>
					</div>

					{/* Modern Action Buttons */}
					<div className='flex gap-3'>
						{/* View Details Button */}
						<button
							onClick={handleViewDetails}
							className='flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-xl font-semibold text-sm transition-all duration-200 bg-gradient-to-r from-blue-500 to-cyan-500 text-white hover:from-blue-600 hover:to-cyan-600 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'>
							<FaExternalLinkAlt className='w-3.5 h-3.5' />
							<span>Explore</span>
						</button>

						{/* Maps Button */}
						<button
							onClick={handleOpenMaps}
							className='flex items-center justify-center gap-2 py-3 px-4 rounded-xl font-semibold text-sm transition-all duration-200 bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-700 hover:bg-white hover:text-gray-900 shadow-md hover:shadow-lg transform hover:-translate-y-0.5'>
							<FaMapMarkerAlt className='w-3.5 h-3.5' />
							<span>Maps</span>
						</button>
					</div>
				</div>
			)}
		</div>
	);

	// Conditional context wrapping - avoid double wrapping if parent already provides context
	const wrapWithContext = (content: React.ReactNode) => {
		// No need for interaction context wrapper with unified system
		return content;
	};

	if (variant === 'inline') {
		return wrapWithContext(
			<div
				className='bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-2xl hover:bg-white transition-all duration-300 h-full flex flex-col overflow-hidden group'
				style={{
					borderColor: colors.ui.gray200,
					background:
						'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)',
				}}>
				{cardContent}
			</div>
		);
	}

	return wrapWithContext(
		<BaseCard
			isVisible={isVisible}
			onClose={onClose}
			config={cardConfig}
			className={
				variant === 'modal'
					? 'absolute bottom-4 left-4 right-4 md:left-4 md:right-auto md:max-w-md z-[200]'
					: ''
			}>
			{cardContent}
		</BaseCard>
	);
};

export default POICard;
